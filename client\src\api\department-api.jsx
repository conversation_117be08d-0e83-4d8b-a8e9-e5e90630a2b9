import axiosInstance from "./axios-instance";

export const createDepartment = async (departmentData) => {
  try {
    const response = await axiosInstance.post("/departments/create", departmentData);
    return response.data;
  } catch (error) {
    console.error(
      "Error creating department:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create department");
  }
};

export const getDepartments = async (schoolId = null) => {
  try {
    const url = schoolId
      ? `/departments?schoolId=${schoolId}`
      : "/departments";
    const response = await axiosInstance.get(url);
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching departments:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch departments");
  }
};

export const getDepartmentById = async (id) => {
  try {
    const response = await axiosInstance.get(`/departments/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching department with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to fetch department with ID ${id}`)
    );
  }
};

export const updateDepartment = async (id, departmentData) => {
  try {
    const response = await axiosInstance.put(`/departments/${id}`, departmentData);
    return response.data;
  } catch (error) {
    console.error(
      `Error updating department with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update department with ID ${id}`)
    );
  }
};

export const deleteDepartment = async (id) => {
  try {
    const response = await axiosInstance.delete(`/departments/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting department with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to delete department with ID ${id}`)
    );
  }
};

export const updateDepartmentStatus = async (id, status) => {
  try {
    const response = await axiosInstance.patch(`/departments/${id}/status`, {
      status,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating department status for department with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update department status for department with ID ${id}`)
    );
  }
};
