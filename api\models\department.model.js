import mongoose from "mongoose";

const DepartmentSchema = new mongoose.Schema(
  {
    // Basic Information
    name: {
      type: String,
      required: [true, "Department name is required"],
      trim: true,
    },
    code: {
      type: String,
      required: [true, "Department code is required"],
      trim: true,
      minlength: [2, "Code must be at least 2 characters"],
      maxlength: [10, "Code must not exceed 10 characters"],
    },
    abbreviation: {
      type: String,
      required: [true, "Abbreviation is required"],
      trim: true,
      maxlength: [20, "Abbreviation must not exceed 20 characters"],
    },
    type: {
      type: String,
      required: [true, "Department type is required"],
      enum: ["Academic", "Administrative", "Support", "Special"],
      trim: true,
    },
    establishedYear: {
      type: Date,
      required: [true, "Established date is required"],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    hasLab: {
      type: Boolean,
      default: false,
    },
    hasCourse: {
      type: Boolean,
      default: false,
    },
    description: {
      type: String,
      required: [true, "Description is required"],
      trim: true,
      minlength: [10, "Description must be at least 10 characters"],
      maxlength: [500, "Description must not exceed 500 characters"],
    },

    // Department Head Information
    head: {
      type: String,
      required: [true, "Department head name is required"],
      trim: true,
      minlength: [2, "Name must be at least 2 characters"],
    },
    headTitle: {
      type: String,
      required: [true, "Head title is required"],
      trim: true,
    },
    headEmail: {
      type: String,
      required: [true, "Head email is required"],
      trim: true,
      match: [
        /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
        "Please enter a valid email address",
      ],
    },
    headPhone: {
      type: String,
      required: [true, "Head phone number is required"],
      trim: true,
    },

    // Relationships
    schoolId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "School",
      required: [true, "School ID is required"],
    },

    // Status
    status: {
      type: String,
      enum: ["active", "inactive"],
      default: "active",
    },
  },
  {
    timestamps: true,
  }
);

DepartmentSchema.index({ schoolId: 1 });
DepartmentSchema.index({ code: 1, schoolId: 1 }, { unique: true });
DepartmentSchema.index({ name: 1, schoolId: 1 });

export default mongoose.model("Department", DepartmentSchema);
