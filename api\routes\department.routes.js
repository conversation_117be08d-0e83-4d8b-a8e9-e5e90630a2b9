import express from "express";
import {
  createDepartment,
  getAllDepartments,
  getDepartmentById,
  updateDepartment,
  deleteDepartment,
  updateDepartmentStatus,
} from "../controllers/department.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin"]),
  createDepartment
);
router.get(
  "/",
  protect,
  authorize(["admin", "school-admin"]),
  getAllDepartments
);
router.get(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  getDepartmentById
);
router.put(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  updateDepartment
);
router.delete(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  deleteDepartment
);
router.patch(
  "/:id/status",
  protect,
  authorize(["admin", "school-admin"]),
  updateDepartmentStatus
);

export default router;
