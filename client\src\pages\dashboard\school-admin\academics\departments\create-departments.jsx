import React, { useState } from "react";
import { Container } from "@/components/ui/container";
import { DepartmentForm } from "@/components/forms/dashboard/academics/departments/department-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";

const CreateDepartments = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(!!id);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Department" : "Create New Department"}
        actions={[
          {
            label: "Back to Departments",
            href: "/dashboard/academics/departments",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Academics", href: "/dashboard/academics" },
          { label: "Departments", href: "/dashboard/academics/departments" },
          { label: id ? "Edit Department" : "Create Department" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <DepartmentForm editingId={id} initialData={[]} />
      )}
    </Container>
  );
};

export default CreateDepartments;
