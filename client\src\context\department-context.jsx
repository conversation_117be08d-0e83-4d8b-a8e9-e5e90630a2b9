import { createContext, useContext, useState } from "react";
import {
  createDepartment,
  getDepartments,
  getDepartmentById,
  updateDepartment,
  deleteDepartment,
  updateDepartmentStatus,
} from "@/api/department-api";

const DepartmentContext = createContext({
  departments: [],
  currentDepartment: null,
  isLoading: false,
  error: null,
  addDepartment: () => {},
  fetchAllDepartments: () => {},
  fetchDepartmentById: () => {},
  editDepartment: () => {},
  removeDepartment: () => {},
  updateStatus: () => {},
});

export const DepartmentProvider = ({ children }) => {
  const [departments, setDepartments] = useState([]);
  const [currentDepartment, setCurrentDepartment] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const addDepartment = async (departmentData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createDepartment(departmentData);
      setDepartments((prevDepartments) => [...prevDepartments, response.data]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create department");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllDepartments = async (schoolId = null) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getDepartments(schoolId);
      setDepartments(response.data);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to fetch departments");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchDepartmentById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getDepartmentById(id);
      setCurrentDepartment(response.data);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || `Failed to fetch department with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editDepartment = async (id, departmentData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateDepartment(id, departmentData);
      setDepartments(
        departments.map((dept) =>
          dept._id === id ? response.data : dept
        )
      );
      if (currentDepartment && currentDepartment._id === id) {
        setCurrentDepartment(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update department with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeDepartment = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteDepartment(id);
      setDepartments(departments.filter((dept) => dept._id !== id));
      if (currentDepartment && currentDepartment._id === id) {
        setCurrentDepartment(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete department with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateStatus = async (id, status) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateDepartmentStatus(id, status);
      setDepartments(
        departments.map((dept) =>
          dept._id === id ? response.data : dept
        )
      );
      if (currentDepartment && currentDepartment._id === id) {
        setCurrentDepartment(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(
        error.message || `Failed to update department status with ID: ${id}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  // Create department options for select inputs
  const departmentOptions = departments.map((dept) => ({
    label: dept.name,
    value: dept._id,
    id: dept._id,
    name: dept.name,
    code: dept.code,
  }));

  return (
    <DepartmentContext.Provider
      value={{
        departments,
        setDepartments,
        currentDepartment,
        isLoading,
        error,
        addDepartment,
        fetchAllDepartments,
        fetchDepartmentById,
        editDepartment,
        removeDepartment,
        updateStatus,
        departmentOptions,
      }}
    >
      {children}
    </DepartmentContext.Provider>
  );
};

export const useDepartment = () => useContext(DepartmentContext);
